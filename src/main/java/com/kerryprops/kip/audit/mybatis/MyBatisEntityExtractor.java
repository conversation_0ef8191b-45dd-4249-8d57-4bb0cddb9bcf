package com.kerryprops.kip.audit.mybatis;

import com.kerryprops.kip.audit.AuditEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.mapping.MappedStatement;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.lang.Nullable;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.Map;

/**
 * MyBatis实体提取器
 * 从MyBatis参数中提取实体对象用于审计
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class MyBatisEntityExtractor {

    private final MyBatisAuditProperties mybatisAuditProperties;

    /**
     * 从MyBatis参数中提取实体对象
     *
     * @param parameter       MyBatis参数对象
     * @param mappedStatement MyBatis映射语句
     * @return 提取的实体对象，如果无法提取则返回null
     */
    @Nullable
    public Object extractEntity(@Nullable Object parameter, @Nullable MappedStatement mappedStatement) {
        if (parameter == null) {
            log.debug("Parameter is null for statement: {}",
                    mappedStatement != null ? mappedStatement.getId() : "unknown");
            return null;
        }

        log.debug("Extracting entity from parameter type: {} for statement: {}",
                parameter.getClass().getSimpleName(),
                mappedStatement != null ? mappedStatement.getId() : "unknown");

        // 1. 直接检查参数是否为实体对象
        if (isAuditableEntity(parameter)) {
            log.debug("Parameter is directly an auditable entity: {}", parameter.getClass().getSimpleName());
            return parameter;
        }

        // 2. 如果参数是Map，尝试从Map中提取实体
        if (parameter instanceof Map) {
            Object entity = extractFromMap((Map<?, ?>) parameter);
            if (entity != null) {
                log.debug("Extracted entity from Map: {}", entity.getClass().getSimpleName());
                return entity;
            }
        }

        // 3. 如果参数是Collection，尝试从Collection中提取实体
        if (parameter instanceof Collection) {
            Object entity = extractFromCollection((Collection<?>) parameter);
            if (entity != null) {
                log.debug("Extracted entity from Collection: {}", entity.getClass().getSimpleName());
                return entity;
            }
        }

        // 4. 尝试通过反射从复杂对象中提取实体
        Object entity = extractFromComplexObject(parameter);
        if (entity != null) {
            log.debug("Extracted entity from complex object: {}", entity.getClass().getSimpleName());
            return entity;
        }

        log.debug("No auditable entity found in parameter for statement: {}",
                mappedStatement != null ? mappedStatement.getId() : "unknown");
        return null;
    }

    /**
     * 从Map参数中提取实体对象
     */
    @Nullable
    private Object extractFromMap(Map<?, ?> parameterMap) {
        // 检查常见的参数名
        for (String commonKey : mybatisAuditProperties.getCommonEntityKeys()) {
            Object value = parameterMap.get(commonKey);
            if (isAuditableEntity(value)) {
                return value;
            }
        }

        // 遍历所有值，查找可审计的实体
        for (Object value : parameterMap.values()) {
            if (isAuditableEntity(value)) {
                return value;
            }

            // 递归检查嵌套对象
            if (value instanceof Map) {
                Object nestedEntity = extractFromMap((Map<?, ?>) value);
                if (nestedEntity != null) {
                    return nestedEntity;
                }
            } else if (value instanceof Collection) {
                Object nestedEntity = extractFromCollection((Collection<?>) value);
                if (nestedEntity != null) {
                    return nestedEntity;
                }
            }
        }

        return null;
    }

    /**
     * 从Collection参数中提取实体对象
     */
    @Nullable
    private Object extractFromCollection(Collection<?> collection) {
        if (collection.isEmpty()) {
            return null;
        }

        // 取第一个元素进行检查（批量操作通常实体类型相同）
        Object firstElement = collection.iterator().next();

        if (isAuditableEntity(firstElement)) {
            return firstElement;
        }

        // 如果第一个元素是Map，尝试从中提取
        if (firstElement instanceof Map) {
            return extractFromMap((Map<?, ?>) firstElement);
        }

        return null;
    }

    /**
     * 从复杂对象中通过反射提取实体
     */
    @Nullable
    private Object extractFromComplexObject(Object parameter) {
        Class<?> parameterClass = parameter.getClass();

        // 跳过基本类型和常见的非实体类型
        if (isSimpleType(parameterClass)) {
            return null;
        }

        try {
            Field[] fields = parameterClass.getDeclaredFields();

            for (Field field : fields) {
                field.setAccessible(true);
                Object fieldValue = field.get(parameter);

                if (isAuditableEntity(fieldValue)) {
                    return fieldValue;
                }

                // 递归检查嵌套对象
                if (fieldValue != null && !isSimpleType(fieldValue.getClass())) {
                    if (fieldValue instanceof Map) {
                        Object nestedEntity = extractFromMap((Map<?, ?>) fieldValue);
                        if (nestedEntity != null) {
                            return nestedEntity;
                        }
                    } else if (fieldValue instanceof Collection) {
                        Object nestedEntity = extractFromCollection((Collection<?>) fieldValue);
                        if (nestedEntity != null) {
                            return nestedEntity;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Error extracting entity from complex object: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 检查对象是否为可审计的实体
     */
    private boolean isAuditableEntity(@Nullable Object obj) {
        if (obj == null) {
            return false;
        }

        return AnnotationUtils.findAnnotation(obj.getClass(), AuditEntity.class) != null;
    }

    /**
     * 检查类型是否为简单类型（不需要进一步检查的类型）
     */
    private boolean isSimpleType(Class<?> clazz) {
        return clazz.isPrimitive() ||
                clazz.equals(String.class) ||
                clazz.equals(Boolean.class) ||
                clazz.equals(Byte.class) ||
                clazz.equals(Short.class) ||
                clazz.equals(Integer.class) ||
                clazz.equals(Long.class) ||
                clazz.equals(Float.class) ||
                clazz.equals(Double.class) ||
                clazz.equals(Character.class) ||
                Number.class.isAssignableFrom(clazz) ||
                clazz.getPackage() != null && clazz.getPackage().getName().startsWith("java.");
    }
}
